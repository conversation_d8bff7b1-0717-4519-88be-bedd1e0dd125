#!/usr/bin/env python3
"""
Analyze real SIH data to understand what drives competition
"""

import pandas as pd
import numpy as np

def analyze_competition():
    df = pd.read_csv('unified_sih_data.csv')
    
    print("📊 REAL SIH DATA ANALYSIS")
    print("=" * 60)
    print(f"Total problems: {len(df)}")
    print(f"Average submissions: {df['submission_count'].mean():.1f}")
    print(f"Median submissions: {df['submission_count'].median():.1f}")
    print(f"Max submissions: {df['submission_count'].max():.0f}")
    print(f"Min submissions: {df['submission_count'].min():.0f}")
    
    # High competition analysis
    print("\n🔴 HIGH COMPETITION PROBLEMS (>100 submissions):")
    print("=" * 80)
    high_comp = df[df['submission_count'] > 100].sort_values('submission_count', ascending=False)
    
    for _, row in high_comp.head(15).iterrows():
        title = str(row['title'])[:70] + "..." if len(str(row['title'])) > 70 else str(row['title'])
        subs = int(row['submission_count'])
        domain = str(row['domain'])
        category = str(row['category'])
        year = int(row['year'])
        print(f"{subs:3d} subs: {title}")
        print(f"      {domain} | {category} | {year}")
        print()
    
    # Low competition analysis
    print("\n🟢 LOW COMPETITION PROBLEMS (<20 submissions):")
    print("=" * 80)
    low_comp = df[df['submission_count'] < 20].sort_values('submission_count', ascending=True)
    
    for _, row in low_comp.head(15).iterrows():
        title = str(row['title'])[:70] + "..." if len(str(row['title'])) > 70 else str(row['title'])
        subs = int(row['submission_count'])
        domain = str(row['domain'])
        category = str(row['category'])
        year = int(row['year'])
        print(f"{subs:3d} subs: {title}")
        print(f"      {domain} | {category} | {year}")
        print()
    
    # Domain analysis
    print("\n📊 COMPETITION BY DOMAIN:")
    print("=" * 50)
    domain_stats = df.groupby('domain')['submission_count'].agg(['mean', 'count']).sort_values('mean', ascending=False)
    for domain, stats in domain_stats.iterrows():
        if stats['count'] >= 5:  # Only domains with 5+ problems
            print(f"{stats['mean']:6.1f} avg | {int(stats['count']):2d} problems | {domain}")
    
    # Category analysis
    print("\n📊 COMPETITION BY CATEGORY:")
    print("=" * 40)
    cat_stats = df.groupby('category')['submission_count'].agg(['mean', 'count']).sort_values('mean', ascending=False)
    for category, stats in cat_stats.iterrows():
        print(f"{stats['mean']:6.1f} avg | {int(stats['count']):3d} problems | {category}")
    
    # Year analysis
    print("\n📊 COMPETITION BY YEAR:")
    print("=" * 30)
    year_stats = df.groupby('year')['submission_count'].agg(['mean', 'count']).sort_values('year')
    for year, stats in year_stats.iterrows():
        print(f"{int(year)} | {stats['mean']:6.1f} avg | {int(stats['count']):3d} problems")
    
    # Keyword analysis
    print("\n🔍 HIGH COMPETITION KEYWORDS:")
    print("=" * 40)
    
    # Find common words in high competition titles
    high_titles = ' '.join(high_comp['title'].fillna('').astype(str)).lower()
    low_titles = ' '.join(low_comp['title'].fillna('').astype(str)).lower()
    
    common_words = ['ai', 'machine learning', 'blockchain', 'iot', 'mobile', 'app', 'system', 'management', 
                   'smart', 'detection', 'monitoring', 'prediction', 'analysis', 'automation', 'platform',
                   'web', 'application', 'development', 'solution', 'technology', 'digital', 'data',
                   'healthcare', 'medical', 'traffic', 'agriculture', 'education', 'security']
    
    print("Words more common in HIGH competition problems:")
    for word in common_words:
        high_count = high_titles.count(word)
        low_count = low_titles.count(word)
        if high_count > 0:
            ratio = high_count / max(low_count, 1)
            if ratio > 1.5:
                print(f"  '{word}': {ratio:.1f}x more common in high competition")
    
    # Find AI/ML problems specifically
    print("\n🤖 AI/ML PROBLEMS ANALYSIS:")
    print("=" * 40)
    ai_keywords = ['ai', 'artificial intelligence', 'machine learning', 'deep learning', 'neural', 'cnn', 'rnn']
    ai_problems = df[df['title'].fillna('').str.lower().str.contains('|'.join(ai_keywords))]
    
    print(f"AI/ML problems: {len(ai_problems)}")
    print(f"Average submissions: {ai_problems['submission_count'].mean():.1f}")
    print(f"Median submissions: {ai_problems['submission_count'].median():.1f}")
    
    print("\nTop AI/ML problems by submissions:")
    ai_top = ai_problems.sort_values('submission_count', ascending=False).head(10)
    for _, row in ai_top.iterrows():
        title = str(row['title'])[:60] + "..." if len(str(row['title'])) > 60 else str(row['title'])
        subs = int(row['submission_count'])
        print(f"  {subs:3d} subs: {title}")

if __name__ == "__main__":
    analyze_competition()
