#!/usr/bin/env python3
"""
Predict Competition for Custom SIH Problem
Usage: python predict_custom_problem.py
"""

import pandas as pd
import numpy as np
import pickle
import sys
import os
import warnings
warnings.filterwarnings('ignore')

class CustomSIHPredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.metadata = {}
        self.load_models()
    
    def load_models(self):
        """Load pre-trained models"""
        if not os.path.exists('models'):
            print("❌ Models directory not found! Run train_and_save_model.py first!")
            sys.exit(1)
        
        print("📦 Loading pre-trained models...")
        
        # Load models
        with open('models/random_forest.pkl', 'rb') as f:
            self.models['rf'] = pickle.load(f)
        
        with open('models/gradient_boosting.pkl', 'rb') as f:
            self.models['gb'] = pickle.load(f)
        
        with open('models/neural_network.pkl', 'rb') as f:
            self.models['nn'] = pickle.load(f)
        
        # Load scalers and encoders
        with open('models/scalers.pkl', 'rb') as f:
            self.scalers = pickle.load(f)
        
        with open('models/encoders.pkl', 'rb') as f:
            self.encoders = pickle.load(f)
        
        # Load metadata
        with open('models/metadata.pkl', 'rb') as f:
            self.metadata = pickle.load(f)
        
        print("✅ Models loaded successfully!")
    
    def get_user_input(self):
        """Get problem details from user"""
        print("\n🎯 ENTER PROBLEM DETAILS:")
        print("=" * 40)
        
        title = input("Problem Title: ").strip()
        if not title:
            print("❌ Title is required!")
            sys.exit(1)
        
        print("\nAvailable Domains:")
        domains = [
            "Agriculture / FoodTech", "MedTech / BioTech / HealthTech", "Smart Vehicles",
            "Renewable / Sustainable Energy", "Smart Automation", "Blockchain & Cybersecurity",
            "Clean & Green Technology", "Disaster Management", "Smart Education",
            "Transportation & Logistics", "Robotics and Drones", "Miscellaneous",
            "Heritage & Culture", "Travel & Tourism", "Fitness & Sports"
        ]
        
        for i, domain in enumerate(domains, 1):
            print(f"{i:2d}. {domain}")
        
        while True:
            try:
                domain_choice = int(input("\nSelect Domain (number): "))
                if 1 <= domain_choice <= len(domains):
                    domain = domains[domain_choice - 1]
                    break
                else:
                    print("❌ Invalid choice! Please select a valid number.")
            except ValueError:
                print("❌ Please enter a valid number!")
        
        print("\nAvailable Categories:")
        categories = ["Software", "Hardware"]
        for i, cat in enumerate(categories, 1):
            print(f"{i}. {cat}")
        
        while True:
            try:
                cat_choice = int(input("\nSelect Category (number): "))
                if 1 <= cat_choice <= len(categories):
                    category = categories[cat_choice - 1]
                    break
                else:
                    print("❌ Invalid choice! Please select 1 or 2.")
            except ValueError:
                print("❌ Please enter a valid number!")
        
        organization = input("\nOrganization (optional): ").strip()
        if not organization:
            organization = "Custom Organization"
        
        return {
            'title': title,
            'domain': domain,
            'category': category,
            'organization': organization
        }
    
    def extract_text_features(self, text):
        """Extract basic text features"""
        if pd.isna(text) or not text:
            return {
                'word_count': 0,
                'char_count': 0,
                'sentence_count': 0,
                'avg_word_length': 0,
                'complexity_score': 0
            }
        
        text = str(text)
        words = text.split()
        sentences = text.split('.')
        
        return {
            'word_count': len(words),
            'char_count': len(text),
            'sentence_count': len(sentences),
            'avg_word_length': np.mean([len(w) for w in words]) if words else 0,
            'complexity_score': len([w for w in words if len(w) > 6]) / len(words) if words else 0
        }
    
    def check_keywords(self, text):
        """Check for various keywords"""
        if pd.isna(text) or not text:
            return {
                'has_ai_keywords': False,
                'has_blockchain_keywords': False,
                'has_health_keywords': False,
                'has_iot_keywords': False
            }
        
        text = str(text).lower()
        
        ai_keywords = ['ai', 'artificial intelligence', 'machine learning', 'deep learning', 'neural', 'ml']
        blockchain_keywords = ['blockchain', 'crypto', 'smart contract', 'distributed ledger']
        health_keywords = ['health', 'medical', 'healthcare', 'patient', 'diagnosis', 'treatment']
        iot_keywords = ['iot', 'internet of things', 'sensor', 'smart device', 'connected']
        
        return {
            'has_ai_keywords': any(kw in text for kw in ai_keywords),
            'has_blockchain_keywords': any(kw in text for kw in blockchain_keywords),
            'has_health_keywords': any(kw in text for kw in health_keywords),
            'has_iot_keywords': any(kw in text for kw in iot_keywords)
        }
    
    def engineer_features(self, problem_data):
        """Engineer features for prediction"""
        print("🛠️ Engineering features...")
        
        # Create dataframe
        df = pd.DataFrame([problem_data])
        
        # Assume 2024 for new problems
        df['year'] = 2024
        
        # YEAR FEATURES
        df['is_2024'] = 1
        df['is_2022'] = 0
        df['year_normalized'] = 1.0
        
        # Extract text features
        text_features = self.extract_text_features(problem_data['title'])
        for feature, value in text_features.items():
            df[feature] = value
        
        # Extract keyword features
        keyword_features = self.check_keywords(problem_data['title'])
        for feature, value in keyword_features.items():
            df[feature] = value
        
        # DOMAIN FLAGS
        high_comp_domains = self.metadata['high_comp_domains']
        low_comp_domains = self.metadata['low_comp_domains']
        
        df['is_high_comp_domain'] = df['domain'].isin(high_comp_domains).astype(int)
        df['is_low_comp_domain'] = df['domain'].isin(low_comp_domains).astype(int)
        
        # CATEGORY FLAGS
        df['is_hardware'] = (df['category'] == 'Hardware').astype(int)
        
        # KEYWORD COMBINATIONS
        df['ai_health_combo'] = (df['has_ai_keywords'] & df['has_health_keywords']).astype(int)
        df['ai_iot_combo'] = (df['has_ai_keywords'] & df['has_iot_keywords']).astype(int)
        
        # TEXT FEATURES
        df['title_length'] = df['title'].str.len()
        df['has_long_title'] = (df['title_length'] > 100).astype(int)
        
        # ORGANIZATION FLAGS
        df['is_isro'] = df['organization'].str.contains('ISRO', na=False).astype(int)
        df['is_aicte'] = df['organization'].str.contains('AICTE', na=False).astype(int)
        
        # INTERACTION FEATURES
        df['domain_year_interaction'] = df['is_high_comp_domain'] * df['is_2024']
        
        return df
    
    def prepare_features(self, data):
        """Prepare features for prediction"""
        print("🎯 Preparing features for prediction...")
        
        # Encode categorical variables using saved encoders
        categorical_cols = ['domain', 'category', 'organization']
        
        for col in categorical_cols:
            if col in self.encoders:
                # Handle unseen categories
                value = data[col].iloc[0]
                if value in self.encoders[col].classes_:
                    data[f'{col}_encoded'] = self.encoders[col].transform([value])[0]
                else:
                    data[f'{col}_encoded'] = 0
            else:
                data[f'{col}_encoded'] = 0
        
        # Select features in the same order as training
        feature_names = self.metadata['feature_names']
        X = data[feature_names]
        
        return X
    
    def predict_competition(self, X):
        """Make ensemble predictions"""
        # Get predictions from each model
        rf_pred = self.models['rf'].predict(X)[0]
        gb_pred = self.models['gb'].predict(X)[0]
        
        X_scaled = self.scalers['standard'].transform(X)
        nn_pred = self.models['nn'].predict(X_scaled)[0]
        
        # Weighted ensemble
        ensemble_pred = (
            0.4 * gb_pred +
            0.35 * rf_pred +
            0.25 * nn_pred
        )
        
        return {
            'ensemble': ensemble_pred,
            'random_forest': rf_pred,
            'gradient_boosting': gb_pred,
            'neural_network': nn_pred
        }
    
    def get_competition_level(self, prediction):
        """Get competition level description"""
        if prediction < 50:
            return "🟢 Low Competition", "Great opportunity! Lower competition expected."
        elif prediction < 150:
            return "🟡 Medium Competition", "Moderate competition expected."
        elif prediction < 300:
            return "🟠 High Competition", "High competition expected. Prepare well!"
        else:
            return "🔴 Ultra-High Competition", "Extremely high competition! Top-tier preparation needed."
    
    def predict_custom_problem(self):
        """Main prediction function"""
        # Get user input
        problem_data = self.get_user_input()
        
        # Engineer features
        data = self.engineer_features(problem_data)
        
        # Prepare features
        X = self.prepare_features(data)
        
        # Predict competition
        print("\n🔮 Predicting competition level...")
        predictions = self.predict_competition(X)
        
        # Display results
        ensemble_pred = predictions['ensemble']
        level, description = self.get_competition_level(ensemble_pred)
        
        print(f"\n🎯 PREDICTION RESULTS:")
        print("=" * 50)
        print(f"Problem: {problem_data['title']}")
        print(f"Domain: {problem_data['domain']}")
        print(f"Category: {problem_data['category']}")
        print()
        print(f"📊 Predicted Submissions: {ensemble_pred:.0f}")
        print(f"🏆 Competition Level: {level}")
        print(f"💡 {description}")
        
        print(f"\n🔍 Individual Model Predictions:")
        print(f"  Random Forest:     {predictions['random_forest']:.0f}")
        print(f"  Gradient Boosting: {predictions['gradient_boosting']:.0f}")
        print(f"  Neural Network:    {predictions['neural_network']:.0f}")
        print(f"  Ensemble Average:  {ensemble_pred:.0f}")
        
        # Feature insights
        print(f"\n💡 KEY FACTORS:")
        if data['is_high_comp_domain'].iloc[0]:
            print("  ✅ High-competition domain (Agriculture/MedTech/Smart Vehicles/Renewable Energy)")
        if data['has_ai_keywords'].iloc[0]:
            print("  ✅ Contains AI/ML keywords - increases competition")
        if data['is_hardware'].iloc[0]:
            print("  ✅ Hardware category - typically gets more submissions")
        if data['title_length'].iloc[0] > 100:
            print("  ✅ Long detailed title - indicates complex problem")

def main():
    print("🚀 CUSTOM SIH PROBLEM COMPETITION PREDICTOR")
    print("=" * 50)
    
    predictor = CustomSIHPredictor()
    predictor.predict_custom_problem()
    
    # Ask if user wants to predict another
    while True:
        again = input("\n🔄 Predict another problem? (y/n): ").strip().lower()
        if again in ['y', 'yes']:
            print("\n" + "="*50)
            predictor.predict_custom_problem()
        elif again in ['n', 'no']:
            print("\n👋 Thanks for using the SIH Competition Predictor!")
            break
        else:
            print("Please enter 'y' or 'n'")

if __name__ == "__main__":
    main()
