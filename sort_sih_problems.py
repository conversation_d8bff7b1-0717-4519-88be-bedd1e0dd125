#!/usr/bin/env python3
"""
Sort SIH Problems by Predicted Competition Level
Usage: python sort_sih_problems.py SIH_PS_2024.xlsx
"""

import pandas as pd
import numpy as np
import pickle
import sys
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class SIHProblemSorter:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.metadata = {}
        self.load_models()
    
    def load_models(self):
        """Load pre-trained models"""
        if not os.path.exists('models'):
            print("❌ Models directory not found! Run train_and_save_model.py first!")
            sys.exit(1)
        
        print("📦 Loading pre-trained models...")
        
        # Load models
        with open('models/random_forest.pkl', 'rb') as f:
            self.models['rf'] = pickle.load(f)
        
        with open('models/gradient_boosting.pkl', 'rb') as f:
            self.models['gb'] = pickle.load(f)
        
        with open('models/neural_network.pkl', 'rb') as f:
            self.models['nn'] = pickle.load(f)
        
        # Load scalers and encoders
        with open('models/scalers.pkl', 'rb') as f:
            self.scalers = pickle.load(f)
        
        with open('models/encoders.pkl', 'rb') as f:
            self.encoders = pickle.load(f)
        
        # Load metadata
        with open('models/metadata.pkl', 'rb') as f:
            self.metadata = pickle.load(f)
        
        print("✅ Models loaded successfully!")
    
    def extract_text_features(self, text):
        """Extract basic text features"""
        if pd.isna(text) or not text:
            return {
                'word_count': 0,
                'char_count': 0,
                'sentence_count': 0,
                'avg_word_length': 0,
                'complexity_score': 0
            }
        
        text = str(text)
        words = text.split()
        sentences = text.split('.')
        
        return {
            'word_count': len(words),
            'char_count': len(text),
            'sentence_count': len(sentences),
            'avg_word_length': np.mean([len(w) for w in words]) if words else 0,
            'complexity_score': len([w for w in words if len(w) > 6]) / len(words) if words else 0
        }
    
    def check_keywords(self, text):
        """Check for various keywords"""
        if pd.isna(text) or not text:
            return {
                'has_ai_keywords': False,
                'has_blockchain_keywords': False,
                'has_health_keywords': False,
                'has_iot_keywords': False
            }
        
        text = str(text).lower()
        
        ai_keywords = ['ai', 'artificial intelligence', 'machine learning', 'deep learning', 'neural', 'ml']
        blockchain_keywords = ['blockchain', 'crypto', 'smart contract', 'distributed ledger']
        health_keywords = ['health', 'medical', 'healthcare', 'patient', 'diagnosis', 'treatment']
        iot_keywords = ['iot', 'internet of things', 'sensor', 'smart device', 'connected']
        
        return {
            'has_ai_keywords': any(kw in text for kw in ai_keywords),
            'has_blockchain_keywords': any(kw in text for kw in blockchain_keywords),
            'has_health_keywords': any(kw in text for kw in health_keywords),
            'has_iot_keywords': any(kw in text for kw in iot_keywords)
        }
    
    def engineer_features(self, df):
        """Engineer features for prediction"""
        print("🛠️ Engineering features...")
        
        data = df.copy()
        
        # Assume 2024 for new problems
        data['year'] = 2024
        
        # YEAR FEATURES
        data['is_2024'] = 1
        data['is_2022'] = 0
        data['year_normalized'] = 1.0
        
        # Extract text features
        text_features = data['title'].apply(self.extract_text_features)
        for feature in ['word_count', 'char_count', 'sentence_count', 'avg_word_length', 'complexity_score']:
            data[feature] = [tf[feature] for tf in text_features]
        
        # Extract keyword features
        keyword_features = data['title'].apply(self.check_keywords)
        for feature in ['has_ai_keywords', 'has_blockchain_keywords', 'has_health_keywords', 'has_iot_keywords']:
            data[feature] = [kf[feature] for kf in keyword_features]
        
        # DOMAIN FLAGS
        high_comp_domains = self.metadata['high_comp_domains']
        low_comp_domains = self.metadata['low_comp_domains']
        
        data['is_high_comp_domain'] = data['domain'].isin(high_comp_domains).astype(int)
        data['is_low_comp_domain'] = data['domain'].isin(low_comp_domains).astype(int)
        
        # CATEGORY FLAGS
        data['is_hardware'] = (data['category'] == 'Hardware').astype(int)
        
        # KEYWORD COMBINATIONS
        data['ai_health_combo'] = (data['has_ai_keywords'] & data['has_health_keywords']).astype(int)
        data['ai_iot_combo'] = (data['has_ai_keywords'] & data['has_iot_keywords']).astype(int)
        
        # TEXT FEATURES
        data['title_length'] = data['title'].str.len()
        data['has_long_title'] = (data['title_length'] > 100).astype(int)
        
        # ORGANIZATION FLAGS
        data['is_isro'] = data['organization'].str.contains('ISRO', na=False).astype(int)
        data['is_aicte'] = data['organization'].str.contains('AICTE', na=False).astype(int)
        
        # INTERACTION FEATURES
        data['domain_year_interaction'] = data['is_high_comp_domain'] * data['is_2024']
        
        return data
    
    def prepare_features(self, data):
        """Prepare features for prediction"""
        print("🎯 Preparing features for prediction...")
        
        # Encode categorical variables using saved encoders
        categorical_cols = ['domain', 'category', 'organization']
        
        for col in categorical_cols:
            if col in self.encoders:
                # Handle unseen categories
                data[f'{col}_encoded'] = data[col].astype(str).apply(
                    lambda x: self.encoders[col].transform([x])[0] 
                    if x in self.encoders[col].classes_ 
                    else 0
                )
            else:
                data[f'{col}_encoded'] = 0
        
        # Select features in the same order as training
        feature_names = self.metadata['feature_names']
        X = data[feature_names]
        
        return X
    
    def predict_competition(self, X):
        """Make ensemble predictions"""
        # Get predictions from each model
        rf_pred = self.models['rf'].predict(X)
        gb_pred = self.models['gb'].predict(X)
        
        X_scaled = self.scalers['standard'].transform(X)
        nn_pred = self.models['nn'].predict(X_scaled)
        
        # Weighted ensemble
        ensemble_pred = (
            0.4 * gb_pred +
            0.35 * rf_pred +
            0.25 * nn_pred
        )
        
        return ensemble_pred
    
    def load_sih_file(self, filepath):
        """Load SIH problem statement file"""
        print(f"📂 Loading {filepath}...")

        if filepath.endswith('.xlsx'):
            df = pd.read_excel(filepath)
        elif filepath.endswith('.csv'):
            df = pd.read_csv(filepath)
        else:
            print("❌ Unsupported file format! Use .xlsx or .csv")
            sys.exit(1)

        # Map column names to standard format
        column_mapping = {
            'Title': 'title',
            'Category': 'category',
            'Technology_Bucket': 'domain',
            'Organisation': 'organization',
            'Description': 'description'
        }

        df = df.rename(columns=column_mapping)

        # Fill missing values
        df['title'] = df['title'].fillna('Untitled Problem')
        df['category'] = df['category'].fillna('Software')
        df['domain'] = df['domain'].fillna('Miscellaneous')
        df['organization'] = df['organization'].fillna('Unknown Organization')

        print(f"✅ Loaded {len(df)} problems")
        return df
    
    def sort_and_save(self, filepath):
        """Main function to sort problems by predicted competition"""
        # Load file
        df = self.load_sih_file(filepath)
        
        # Engineer features
        data = self.engineer_features(df)
        
        # Prepare features
        X = self.prepare_features(data)
        
        # Predict competition
        print("🔮 Predicting competition levels...")
        predictions = self.predict_competition(X)
        
        # Add predictions to dataframe
        df['predicted_submissions'] = predictions.round().astype(int)
        df['competition_level'] = pd.cut(
            predictions, 
            bins=[0, 50, 150, 300, float('inf')],
            labels=['Low', 'Medium', 'High', 'Ultra-High']
        )
        
        # Sort by predicted submissions (descending)
        df_sorted = df.sort_values('predicted_submissions', ascending=False)
        
        # Save results
        output_file = f"sorted_{Path(filepath).stem}.xlsx"
        df_sorted.to_excel(output_file, index=False)
        
        print(f"\n🎯 RESULTS:")
        print(f"📊 Competition Level Distribution:")
        print(df['competition_level'].value_counts())
        
        print(f"\n🔥 TOP 10 HIGHEST COMPETITION PROBLEMS:")
        for i, (_, row) in enumerate(df_sorted.head(10).iterrows(), 1):
            title = str(row['title'])[:60] + "..." if len(str(row['title'])) > 60 else str(row['title'])
            print(f"{i:2d}. {row['predicted_submissions']:3d} submissions - {title}")
            print(f"    Domain: {row.get('domain', 'N/A')} | Category: {row.get('category', 'N/A')}")
        
        print(f"\n💾 Results saved to: {output_file}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python sort_sih_problems.py <SIH_file.xlsx>")
        print("Example: python sort_sih_problems.py SIH_PS_2024.xlsx")
        sys.exit(1)
    
    filepath = sys.argv[1]
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        sys.exit(1)
    
    print("🚀 SIH PROBLEM COMPETITION SORTER")
    print("=" * 50)
    
    sorter = SIHProblemSorter()
    sorter.sort_and_save(filepath)

if __name__ == "__main__":
    main()
